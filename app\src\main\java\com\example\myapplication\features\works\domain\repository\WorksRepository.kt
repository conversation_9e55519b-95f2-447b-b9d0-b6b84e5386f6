package com.example.myapplication.features.works.domain.repository

import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.features.works.data.remote.dto.CreateWorkDto
import com.example.myapplication.features.works.data.remote.dto.GetWorksDto
import com.example.myapplication.features.works.data.remote.dto.WorkDto
import com.example.myapplication.features.works.domain.model.CreateWork
import com.example.myapplication.features.works.domain.model.Work

interface WorksRepository {
    suspend fun createWork(createWorkRequest: CreateWorkDto): ResultAsyncState<WorkDto>
    suspend fun getWorks(): ResultAsyncState<GetWorksDto>
}
