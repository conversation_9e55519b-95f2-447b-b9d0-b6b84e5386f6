package com.example.myapplication.core.di

import com.example.myapplication.data.repository.AuthRepositorylmpl
import com.example.myapplication.features.auth.data.remote.api.AuthApiService
import com.example.myapplication.features.auth.domain.repository.AuthRepository
import com.example.myapplication.features.works.data.remote.api.WorkApiService
import com.example.myapplication.features.works.domain.repository.WorksRepository
import com.example.myapplication.features.works.domain.repository.WorksRepositopyImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object RepositoryModule {

    @Provides
    @Singleton
    fun provideAuthRepository(authApiService: AuthApiService): AuthRepository {
        return AuthRepositorylmpl(authApiService)
    }

    @Provides
    @Singleton
    fun provideWorksRepository(workApiService: WorkApiService): WorksRepository {
        return WorksRepositopyImpl(workApiService)
    }
}
