package com.example.myapplication.features.works.presentation.workList


import App<PERSON><PERSON>rDI
import com.example.myapplication.features.works.domain.useCase.GetWorksUseCase

class WorkListViewModelFactory(
    private val appContainer: AppContainerDI
) : androidx.lifecycle.ViewModelProvider.Factory {
    override fun <T : androidx.lifecycle.ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(WorkListViewModel::class.java)) {
            @Suppress("UNCHECKED_CAST")
            return WorkListViewModel(
                getWorksUseCase = GetWorksUseCase(
                    worksRepository = appContainer.worksRepository
                )
            ) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class: ${modelClass.name}")
    }
}