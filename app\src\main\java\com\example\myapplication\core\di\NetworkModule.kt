package com.example.myapplication.core.di

import android.content.Context
import com.example.myapplication.core.security.TokenManager
import com.example.myapplication.data.remote.api.RetrofitClient
import com.example.myapplication.features.auth.data.remote.api.AuthApiService
import com.example.myapplication.features.works.data.remote.api.WorkApiService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    @Provides
    @Singleton
    fun provideTokenManager(@ApplicationContext context: Context): TokenManager {
        return TokenManager.getInstance(context)
    }

    @Provides
    @Singleton
    fun provideAuthApiService(tokenManager: TokenManager): AuthApiService {
        RetrofitClient.init(tokenManager)
        return RetrofitClient.create(AuthApiService::class.java)
    }

    @Provides
    @Singleton
    fun provideWorkApiService(tokenManager: TokenManager): WorkApiService {
        RetrofitClient.init(tokenManager)
        return RetrofitClient.create(WorkApiService::class.java)
    }
}
