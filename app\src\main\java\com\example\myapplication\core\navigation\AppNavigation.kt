import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.myapplication.core.security.IAuthenticationManagerService
import com.example.myapplication.features.auth.presentation.login.LoginResult
import com.example.myapplication.features.auth.presentation.login.LoginScreen
import com.example.myapplication.features.auth.presentation.login.LoginViewModel
import com.example.myapplication.features.auth.presentation.register.RegisterScreen
import com.example.myapplication.features.auth.presentation.register.RegisterViewModel
import com.example.myapplication.features.home.presentation.HomeScreen
import com.example.myapplication.features.splash.presentation.SplashScreen
import com.example.myapplication.features.splash.presentation.SplashScreenWithError
import dagger.hilt.android.EntryPoint
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@EntryPoint
interface AuthManagerEntryPoint {
    fun authenticationManager(): IAuthenticationManagerService
}

@Composable
fun AppNavigation(
    onShowToast: (String) -> Unit,
    modifier: Modifier
) {
    val context = LocalContext.current
    val authManagerEntryPoint = EntryPointAccessors.fromApplication(
        context,
        AuthManagerEntryPoint::class.java
    )
    val authenticationManager = authManagerEntryPoint.authenticationManager()
    val coroutineScope = rememberCoroutineScope()

    val isAuthenticated by authenticationManager.isAuthenticated.collectAsState()
    val currentUser by authenticationManager.currentUser.collectAsState()

    val currentScreen = remember {  mutableStateOf<Screen>(Screen.SplashScreen) }
    val isLoading = remember { mutableStateOf<Boolean>(true) }
    val errorMessage = remember { mutableStateOf<String?>(null) }

    LaunchedEffect(Unit) {
        try {
            val isUserAuthenticated = authenticationManager.checkAuthenticationStatus()

            isLoading.value = false
            if (isUserAuthenticated) {
                currentScreen.value = Screen.HomeScreen
            } else {
                currentScreen.value = Screen.LoginScreen
            }
        } catch (e: Exception) {
            isLoading.value = false
            errorMessage.value = "Erro ao verificar autenticação: ${e.message}"
            onShowToast("Erro ao verificar autenticação: ${e.message}")
        }
    }

    LaunchedEffect(isAuthenticated, currentUser) {
        if (!isAuthenticated || currentUser == null) {
            if (currentScreen.value != Screen.LoginScreen) {
                currentScreen.value = Screen.LoginScreen
            }
        }
    }

    // Protege a navegação: nunca permite HomeScreen se não autenticado
    if (currentScreen.value == Screen.HomeScreen && (!isAuthenticated || currentUser == null)) {
        currentScreen.value = Screen.LoginScreen
    }

    println(
        "Navegação atual: ${currentScreen.value}, Usuário autenticado: $isAuthenticated, Usuário atual: $currentUser"
    )

    when (currentScreen.value) {
        Screen.SplashScreen -> {
            if (errorMessage.value != null) {
                SplashScreenWithError(
                    errorMessage = errorMessage.value ?: "Erro desconhecido",
                    onRetry = {
                        errorMessage.value = null
                        isLoading.value = true
                        coroutineScope.launch {
                            try {
                                val isUserAuthenticated = authenticationManager.checkAuthenticationStatus()
                                delay(1000)
                                isLoading.value = false
                                if (isUserAuthenticated) {
                                    currentScreen.value = Screen.HomeScreen
                                } else {
                                    currentScreen.value = Screen.LoginScreen
                                }
                            } catch (e: Exception) {
                                isLoading.value = false
                                errorMessage.value = "Erro ao verificar autenticação: ${e.message}"
                            }
                        }
                    },
                )
            } else {
                SplashScreen(
                    isLoading = isLoading.value,
                    loadingMessage = "Verificando autenticação..."
                )
            }
        }

        Screen.LoginScreen -> {
            val loginViewModel: LoginViewModel = hiltViewModel()
            LoginScreen(
                viewModel = loginViewModel,
                onLoginSuccess = { result: LoginResult.Success ->
                    onShowToast(result.message)
                    currentScreen.value = Screen.HomeScreen
                },
                onNavigateToRegister = {
                    currentScreen.value = Screen.RegisterScreen
                }
            )
        }

        Screen.RegisterScreen -> {
            val registerViewModel: RegisterViewModel = hiltViewModel()
            RegisterScreen(
                viewModel = registerViewModel,
                onRegisterSuccess = {
                    onShowToast("Cadastro realizado com sucesso!")
                    currentScreen.value = Screen.LoginScreen
                },
                onLoginClick = {
                    currentScreen.value = Screen.LoginScreen
                }
            )
        }

        Screen.HomeScreen -> {
            LaunchedEffect(currentUser) {
                if (currentUser != null) {
                    onShowToast("Usuário autenticado: ${currentUser?.username}")
                }
            }
            HomeScreen(
                username = currentUser?.username,
                onLogout = {
                    coroutineScope.launch {
                     authenticationManager.logout()
                    }
                },

            )
        }
    }



}