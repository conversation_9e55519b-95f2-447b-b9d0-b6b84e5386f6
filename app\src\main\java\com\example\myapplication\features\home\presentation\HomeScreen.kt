package com.example.myapplication.features.home.presentation

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Settings
import androidx.compose.ui.graphics.vector.ImageVector
import AppContainerDI
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.myapplication.features.profile.presentation.ProfileScreen
import com.example.myapplication.features.profile.presentation.ProfileViewModel
import com.example.myapplication.features.profile.presentation.ProfileViewModelFactory
import com.example.myapplication.features.works.presentation.workList.WorkListScreen
import com.example.myapplication.features.works.presentation.workList.WorkListViewModel
import com.example.myapplication.features.works.presentation.workList.WorkListViewModelFactory

sealed class HomeNavItem(val label: String, val icon: ImageVector) {
    object Dashboard : HomeNavItem("Dashboard", Icons.Default.Home)
    object Profile : HomeNavItem("Perfil", Icons.Default.Person)
    object Settings : HomeNavItem("Configurações", Icons.Default.Settings)
}

@Composable
fun HomeScreen(appContainer: AppContainerDI, username: String? = null, onLogout: () -> Unit = {}) {
    var selectedItem by remember { mutableStateOf<HomeNavItem>(HomeNavItem.Dashboard) }

    Scaffold(
        bottomBar = {
            NavigationBar {
                val items = listOf(
                    HomeNavItem.Dashboard,
                    HomeNavItem.Profile,
                    HomeNavItem.Settings
                )
                items.forEach { item ->
                    NavigationBarItem(
                        icon = { androidx.compose.material3.Icon(item.icon, contentDescription = item.label) },
                        label = { Text(item.label) },
                        selected = selectedItem == item,
                        onClick = { selectedItem = item }
                    )
                }
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize().padding(innerPadding)) {
            when (selectedItem) {
                is HomeNavItem.Dashboard -> {
                   val workListViewModel: WorkListViewModel = viewModel(
                        factory = WorkListViewModelFactory(appContainer)
                    )
                    WorkListScreen(workListViewModel)
                }
                is HomeNavItem.Profile -> {
                    val profileViewModel: ProfileViewModel = viewModel(
                        factory = ProfileViewModelFactory(appContainer)
                    )
                    ProfileScreen(profileViewModel)
                }
                is HomeNavItem.Settings -> {
                    Text(
                        text = "Configurações",
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }
            Button(
                onClick = onLogout,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .offset(y = (-56).dp)
            ) {
                Text(text = "Sair")
            }
        }
    }
}


