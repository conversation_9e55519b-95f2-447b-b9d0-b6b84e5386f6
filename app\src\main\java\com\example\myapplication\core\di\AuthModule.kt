package com.example.myapplication.core.di

import com.example.myapplication.core.security.AuthenticationManagerFactory
import com.example.myapplication.core.security.IAuthenticationManagerService
import com.example.myapplication.core.security.TokenManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AuthModule {

    @Provides
    @Singleton
    fun provideAuthenticationManager(tokenManager: TokenManager): IAuthenticationManagerService {
        return AuthenticationManagerFactory.create(tokenManager = tokenManager)
    }
}
