package com.example.myapplication.features.works.data.remote.api

import com.example.myapplication.data.remote.dto.BaseResponseDto
import com.example.myapplication.features.works.data.remote.dto.CreateWorkDto
import com.example.myapplication.features.works.data.remote.dto.GetWorksDto
import com.example.myapplication.features.works.data.remote.dto.WorkDto
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST


interface WorkApiService {
    @POST("/works")
    suspend fun createWork(@Body request: CreateWorkDto): Response<BaseResponseDto<WorkDto>>

    @GET("/works")
    suspend fun getWorks(): Response<BaseResponseDto<GetWorksDto>>
}
