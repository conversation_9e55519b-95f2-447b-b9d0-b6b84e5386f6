package com.example.myapplication.features.works.domain.repository

import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.features.works.data.remote.api.WorkApiService
import com.example.myapplication.features.works.data.remote.dto.CreateWorkDto
import com.example.myapplication.features.works.data.remote.dto.GetWorksDto
import com.example.myapplication.features.works.data.remote.dto.WorkDto

class WorksRepositopyImpl(
    private val worksService: WorkApiService
): WorksRepository {

    override suspend fun createWork(createWorkRequest: CreateWorkDto): ResultAsyncState<WorkDto> {
        try {
            val response = worksService.createWork(createWorkRequest)
            if (!response.isSuccessful) {
                throw Exception("Erro ao criar obra: ${response.message()}")
            }
        } catch (e: Exception) {
            throw Exception("Erro ao criar obra: ${e.message}")
        }
        return ResultAsyncState.Error( "Erro ao criar obra")
    }

    

    override suspend fun getWorks(): ResultAsyncState<GetWorksDto> {
        return try {
            val response = worksService.getWorks()
            if (response.isSuccessful) {
                val works = response.body()?.data
                ResultAsyncState.Success(
                    works ?: GetWorksDto(
                        works = emptyList(),
                        page = 1,
                        limit = 10,
                        totalPages = 1,
                        total = 1
                    )
                )
            } else {
                ResultAsyncState.Error("Erro ao obter obras: ${response.message()}")
            }
            
        } catch (e: Exception) {
            ResultAsyncState.Error("Erro ao obter obras: ${e.message}")
        }
    }
}